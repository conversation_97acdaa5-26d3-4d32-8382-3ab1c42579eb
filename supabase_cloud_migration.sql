-- =====================================================
-- 1. CREATE GROUPS TABLE (structure first, then columns)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.groups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY
);

-- Add columns
ALTER TABLE public.groups ADD COLUMN IF NOT EXISTS name TEXT NOT NULL;
ALTER TABLE public.groups ADD COLUMN IF NOT EXISTS creator_id UUID NOT NULL;
ALTER TABLE public.groups ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add constraints and indexes
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_constraint WHERE conname = 'groups_name_key'
  ) THEN
    ALTER TABLE public.groups 
    ADD CONSTRAINT groups_name_key UNIQUE (name);
  END IF;
END $$;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_constraint WHERE conname = 'fk_groups_creator'
  ) THEN
    ALTER TABLE public.groups 
    ADD CONSTRAINT fk_groups_creator FOREIGN KEY (creator_id) REFERENCES public.users(id) ON DELETE CASCADE;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_groups_name ON public.groups(name);
CREATE INDEX IF NOT EXISTS idx_groups_creator_id ON public.groups(creator_id);

-- =====================================================
-- 2. CREATE GROUP_MEMBERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.group_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY
);

ALTER TABLE public.group_members ADD COLUMN IF NOT EXISTS group_id UUID NOT NULL;
ALTER TABLE public.group_members ADD COLUMN IF NOT EXISTS user_id UUID NOT NULL;
ALTER TABLE public.group_members ADD COLUMN IF NOT EXISTS joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_constraint WHERE conname = 'group_members_unique'
  ) THEN
    ALTER TABLE public.group_members 
    ADD CONSTRAINT group_members_unique UNIQUE (group_id, user_id);
  END IF;
END $$;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_constraint WHERE conname = 'fk_group_members_group'
  ) THEN
    ALTER TABLE public.group_members 
    ADD CONSTRAINT fk_group_members_group FOREIGN KEY (group_id) REFERENCES public.groups(id) ON DELETE CASCADE;
  END IF;
END $$;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_constraint WHERE conname = 'fk_group_members_user'
  ) THEN
    ALTER TABLE public.group_members 
    ADD CONSTRAINT fk_group_members_user FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_group_members_group_id ON public.group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_group_members_user_id ON public.group_members(user_id);

-- =====================================================
-- 3. UPDATE MESSAGES TABLE FOR GROUP SUPPORT
-- =====================================================

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'group_id'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'message_type'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN message_type TEXT NOT NULL DEFAULT 'individual'
    CHECK (message_type IN ('individual', 'group'));
  END IF;
END $$;

ALTER TABLE public.messages ALTER COLUMN receiver_id DROP NOT NULL;
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS different_sender_receiver;
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS valid_message_type_and_recipients;

ALTER TABLE public.messages ADD CONSTRAINT valid_message_type_and_recipients CHECK (
  (message_type = 'individual' AND receiver_id IS NOT NULL AND group_id IS NULL AND sender_id != receiver_id)
  OR
  (message_type = 'group' AND receiver_id IS NULL AND group_id IS NOT NULL)
);

CREATE INDEX IF NOT EXISTS idx_messages_group_id ON public.messages(group_id);
CREATE INDEX IF NOT EXISTS idx_messages_message_type ON public.messages(message_type);

-- =====================================================
-- 4. CREATE GROUP FUNCTIONS
-- =====================================================

CREATE OR REPLACE FUNCTION create_group_and_join(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  result_group_id UUID,
  result_group_name TEXT,
  result_creator_id UUID,
  result_created_at TIMESTAMP WITH TIME ZONE,
  result_is_member BOOLEAN,
  result_member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_result RECORD;
BEGIN
  SELECT u.id INTO v_user_id FROM users u WHERE u.username = p_username;
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;

  INSERT INTO groups (name, creator_id)
  VALUES (p_group_name, v_user_id)
  RETURNING groups.id, groups.name, groups.creator_id, groups.created_at 
  INTO v_result;

  INSERT INTO group_members (group_id, user_id)
  VALUES (v_result.id, v_user_id);

  RETURN QUERY
  SELECT 
    v_result.id,
    v_result.name,
    v_result.creator_id,
    v_result.created_at,
    TRUE,
    1::BIGINT;
END;
$$;

CREATE OR REPLACE FUNCTION auto_join_group(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  result_group_id UUID,
  result_group_name TEXT,
  result_creator_id UUID,
  result_created_at TIMESTAMP WITH TIME ZONE,
  result_is_member BOOLEAN,
  result_member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_group RECORD;
  v_member_count BIGINT;
BEGIN
  SELECT u.id INTO v_user_id FROM users u WHERE u.username = p_username;
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;

  SELECT g.id, g.name, g.creator_id, g.created_at
  INTO v_group
  FROM groups g
  WHERE LOWER(g.name) = LOWER(p_group_name);

  IF v_group.id IS NULL THEN
    RETURN;
  END IF;

  INSERT INTO group_members (group_id, user_id)
  VALUES (v_group.id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;

  SELECT COUNT(*) INTO v_member_count FROM group_members gm WHERE gm.group_id = v_group.id;

  RETURN QUERY
  SELECT 
    v_group.id,
    v_group.name,
    v_group.creator_id,
    v_group.created_at,
    TRUE,
    v_member_count;
END;
$$;

CREATE OR REPLACE FUNCTION add_user_to_group(p_group_id UUID, p_username TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  SELECT id INTO v_user_id FROM users WHERE username = p_username;

  IF v_user_id IS NULL THEN
    INSERT INTO users (username)
    VALUES (p_username)
    RETURNING id INTO v_user_id;
  END IF;

  INSERT INTO group_members (group_id, user_id)
  VALUES (p_group_id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- =====================================================
-- 5. CREATE READABLE MESSAGES VIEW
-- =====================================================

CREATE OR REPLACE VIEW public.messages_readable AS
SELECT 
  m.id,
  m.sender_id,
  sender.username AS sender_username,
  m.receiver_id,
  receiver.username AS receiver_username,
  m.group_id,
  g.name AS group_name,
  m.message,
  m.morse_code,
  m.message_type,
  m.created_at,
  m.read_at
FROM public.messages m
LEFT JOIN public.users sender ON m.sender_id = sender.id
LEFT JOIN public.users receiver ON m.receiver_id = receiver.id
LEFT JOIN public.groups g ON m.group_id = g.id
ORDER BY m.created_at DESC;

-- =====================================================
-- 6. SET PERMISSIONS
-- =====================================================

GRANT ALL ON public.groups TO postgres, anon, authenticated;
GRANT ALL ON public.group_members TO postgres, anon, authenticated;
GRANT SELECT ON public.messages_readable TO postgres, anon, authenticated;
GRANT EXECUTE ON FUNCTION create_group_and_join TO postgres, anon, authenticated;
GRANT EXECUTE ON FUNCTION auto_join_group TO postgres, anon, authenticated;
GRANT EXECUTE ON FUNCTION add_user_to_group TO postgres, anon, authenticated;

-- =====================================================
-- 7. ADD COMMENTS
-- =====================================================

COMMENT ON TABLE public.groups IS 'Groups for group messaging';
COMMENT ON TABLE public.group_members IS 'Group membership tracking';
COMMENT ON VIEW public.messages_readable IS 'Human-readable view of messages showing usernames instead of just UUIDs';
COMMENT ON FUNCTION create_group_and_join IS 'Creates a new group and adds creator as first member';
COMMENT ON FUNCTION auto_join_group IS 'Searches for a group and auto-joins user if found';
COMMENT ON FUNCTION add_user_to_group IS 'Adds a user to an existing group by username';