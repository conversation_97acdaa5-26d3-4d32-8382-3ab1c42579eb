/**
 * Utility functions for validating and formatting usernames and group names
 */

/**
 * Formats a name according to the rules:
 * - Convert to lowercase
 * - Replace spaces with hyphens
 * - Remove extra spaces/hyphens
 * - Ensure no consecutive hyphens
 */
export const formatName = (name: string): string => {
  if (!name) return '';
  
  return name
    .toLowerCase()                    // Convert to lowercase
    .trim()                          // Remove leading/trailing spaces
    .replace(/\s+/g, '-')           // Replace one or more spaces with single hyphen
    .replace(/-+/g, '-')            // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-+|-+$/g, '');       // Remove leading/trailing hyphens
};

/**
 * Validates a name and returns formatted version with validation result
 */
export const validateAndFormatName = (name: string): { 
  isValid: boolean; 
  formattedName: string; 
  error?: string; 
} => {
  if (!name || !name.trim()) {
    return {
      isValid: false,
      formattedName: '',
      error: 'Name cannot be empty'
    };
  }

  const formatted = formatName(name);
  
  // Check minimum length
  if (formatted.length < 2) {
    return {
      isValid: false,
      formattedName: formatted,
      error: 'Name must be at least 2 characters long'
    };
  }

  // Check maximum length
  if (formatted.length > 50) {
    return {
      isValid: false,
      formattedName: formatted,
      error: 'Name must be 50 characters or less'
    };
  }

  // Check for valid characters (only letters, numbers, and hyphens)
  if (!/^[a-z0-9-]+$/.test(formatted)) {
    return {
      isValid: false,
      formattedName: formatted,
      error: 'Name can only contain letters, numbers, and hyphens'
    };
  }

  // Check that it doesn't start or end with hyphen (should be handled by formatName, but double-check)
  if (formatted.startsWith('-') || formatted.endsWith('-')) {
    return {
      isValid: false,
      formattedName: formatted,
      error: 'Name cannot start or end with a hyphen'
    };
  }

  return {
    isValid: true,
    formattedName: formatted
  };
};

/**
 * Format timestamp to UTC format: "08:12am 20-Aug-2025 UTC"
 */
export const formatTimestampUTC = (timestamp: string): string => {
  const date = new Date(timestamp);
  
  // Format time as 12-hour with am/pm
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
    timeZone: 'UTC'
  };
  
  // Format date as dd-MMM-yyyy
  const dateOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    timeZone: 'UTC'
  };
  
  const time = date.toLocaleTimeString('en-US', timeOptions).toLowerCase();
  const dateStr = date.toLocaleDateString('en-GB', dateOptions).replace(/\s/g, '-');
  
  return `${time} ${dateStr} UTC`;
};
