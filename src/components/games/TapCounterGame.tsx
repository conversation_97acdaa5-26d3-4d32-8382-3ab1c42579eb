
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface TapCounterGameProps {
  onScore: (score: number) => void;
}

const TapCounterGame = ({ onScore }: TapCounterGameProps) => {
  const [taps, setTaps] = useState(0);
  const [timeLeft, setTimeLeft] = useState(10);
  const [gameActive, setGameActive] = useState(false);
  const [intensity, setIntensity] = useState(1);

  const handleTap = () => {
    if (!gameActive) return;
    
    const newTaps = taps + 1;
    setTaps(newTaps);
    setIntensity(1.1);
    setTimeout(() => setIntensity(1), 100);
    onScore(newTaps);
  };

  const startGame = () => {
    setGameActive(true);
    setTaps(0);
    setTimeLeft(10);
  };

  const endGame = () => {
    setGameActive(false);
    onScore(taps);
  };

  useEffect(() => {
    if (!gameActive) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          endGame();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [gameActive, taps]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Velocity</h3>
        <div className="flex gap-4 text-sm text-gray-600">
          <span>Taps: {taps}</span>
          <span>Time: {timeLeft}s</span>
        </div>
      </div>

      <div className="flex justify-center items-center h-48 mb-4">
        <button
          onClick={handleTap}
          disabled={!gameActive}
          className={`w-32 h-32 rounded-full border-2 transition-all duration-100 focus:outline-none ${
            gameActive
              ? 'border-primary bg-primary/5 hover:bg-primary/10 active:bg-primary/20 focus:ring-4 focus:ring-primary/20'
              : 'border-gray-300 bg-gray-100 cursor-not-allowed'
          }`}
          style={{ transform: `scale(${intensity})` }}
        >
          <div className="text-primary text-sm font-medium">
            {gameActive ? 'TAP' : 'READY'}
          </div>
        </button>
      </div>

      <Button
        onClick={gameActive ? endGame : startGame}
        variant={gameActive ? "outline" : "default"}
        className="w-full"
      >
        {gameActive ? 'End Game' : 'Start Game'}
      </Button>
    </div>
  );
};

export default TapCounterGame;
