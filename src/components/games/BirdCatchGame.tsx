
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';

interface BirdCatchGameProps {
  onScore: (score: number) => void;
}

const BirdCatchGame = ({ onScore }: BirdCatchGameProps) => {
  const [targets, setTargets] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const [score, setScore] = useState(0);
  const [gameActive, setGameActive] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15);

  const spawnTarget = useCallback(() => {
    const newTarget = {
      id: Date.now() + Math.random(),
      x: Math.random() * 75 + 12.5, // Keep targets away from edges
      y: Math.random() * 65 + 17.5,
    };
    setTargets(prev => [...prev, newTarget]);
    
    // Remove target after 3 seconds if not caught
    setTimeout(() => {
      setTargets(prev => prev.filter(target => target.id !== newTarget.id));
    }, 3000);
  }, []);

  const catchTarget = (targetId: number) => {
    setTargets(prev => prev.filter(target => target.id !== targetId));
    setScore(prev => prev + 1);
    onScore(score + 1);
  };

  const startGame = () => {
    setGameActive(true);
    setScore(0);
    setTimeLeft(15);
    setTargets([]);
  };

  const endGame = () => {
    setGameActive(false);
    setTargets([]);
    onScore(score);
  };

  useEffect(() => {
    if (!gameActive) return;

    const gameTimer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          endGame();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    const spawnTimer = setInterval(() => {
      if (targets.length < 2) {
        spawnTarget();
      }
    }, 1500);

    return () => {
      clearInterval(gameTimer);
      clearInterval(spawnTimer);
    };
  }, [gameActive, targets.length, spawnTarget]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Precision</h3>
        <div className="flex gap-4 text-sm text-gray-600">
          <span>Score: {score}</span>
          <span>Time: {timeLeft}s</span>
        </div>
      </div>

      <div className="relative h-48 bg-gray-50 rounded-md border-2 border-dashed border-gray-200 overflow-hidden mb-4">
        {targets.map(target => (
          <button
            key={target.id}
            onClick={() => catchTarget(target.id)}
            className="absolute w-4 h-4 bg-primary rounded-full hover:scale-125 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50"
            style={{ left: `${target.x}%`, top: `${target.y}%` }}
          />
        ))}
        {!gameActive && targets.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm">
            Click targets as they appear
          </div>
        )}
      </div>

      <Button
        onClick={gameActive ? endGame : startGame}
        variant={gameActive ? "outline" : "default"}
        className="w-full"
      >
        {gameActive ? 'End Game' : 'Start Game'}
      </Button>
    </div>
  );
};

export default BirdCatchGame;
