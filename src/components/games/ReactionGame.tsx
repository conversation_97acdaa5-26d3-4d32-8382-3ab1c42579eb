
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ReactionGameProps {
  onScore: (score: number) => void;
}

const ReactionGame = ({ onScore }: ReactionGameProps) => {
  const [gameState, setGameState] = useState<'waiting' | 'ready' | 'go' | 'result'>('waiting');
  const [startTime, setStartTime] = useState(0);
  const [reactionTime, setReactionTime] = useState(0);
  const [bestTime, setBestTime] = useState<number | null>(null);

  const startGame = () => {
    setGameState('ready');
    const delay = Math.random() * 3000 + 2000; // 2-5 seconds
    
    setTimeout(() => {
      setGameState('go');
      setStartTime(Date.now());
    }, delay);
  };

  const handleClick = () => {
    if (gameState === 'go') {
      const time = Date.now() - startTime;
      setReactionTime(time);
      setGameState('result');
      
      if (!bestTime || time < bestTime) {
        setBestTime(time);
      }
      
      onScore(Math.max(1000 - time, 0));
    } else if (gameState === 'ready') {
      setGameState('waiting');
    }
  };

  const resetGame = () => {
    setGameState('waiting');
    setReactionTime(0);
  };

  const getStateContent = () => {
    switch (gameState) {
      case 'waiting':
        return {
          bg: 'bg-gray-50',
          text: 'Click Start to begin',
          subtext: '',
          clickable: false
        };
      case 'ready':
        return {
          bg: 'bg-red-50 border-red-200',
          text: 'Wait...',
          subtext: 'Click too early and you\'ll restart',
          clickable: true
        };
      case 'go':
        return {
          bg: 'bg-green-50 border-green-200',
          text: 'CLICK NOW!',
          subtext: '',
          clickable: true
        };
      case 'result':
        return {
          bg: 'bg-blue-50 border-blue-200',
          text: `${reactionTime}ms`,
          subtext: reactionTime < 300 ? 'Excellent' : reactionTime < 500 ? 'Good' : 'Keep practicing',
          clickable: false
        };
    }
  };

  const stateContent = getStateContent();

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Reflex</h3>
        {bestTime && (
          <div className="text-sm text-gray-600">Best: {bestTime}ms</div>
        )}
      </div>

      <div
        className={`h-48 rounded-md border-2 border-dashed flex flex-col items-center justify-center cursor-pointer transition-all duration-300 ${stateContent.bg} ${
          stateContent.clickable ? 'hover:scale-[1.02]' : ''
        }`}
        onClick={stateContent.clickable ? handleClick : undefined}
      >
        <div className="text-2xl font-medium text-gray-800 mb-2">{stateContent.text}</div>
        {stateContent.subtext && (
          <div className="text-sm text-gray-600 text-center">{stateContent.subtext}</div>
        )}
      </div>

      <div className="mt-4">
        <Button
          onClick={gameState === 'result' ? resetGame : startGame}
          disabled={gameState === 'ready' || gameState === 'go'}
          variant={gameState === 'result' ? "outline" : "default"}
          className="w-full"
        >
          {gameState === 'result' ? 'Try Again' : 'Start Game'}
        </Button>
      </div>
    </div>
  );
};

export default ReactionGame;
