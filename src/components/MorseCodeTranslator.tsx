
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X } from 'lucide-react';

interface MorseCodeTranslatorProps {
  onClose: () => void;
}

const MorseCodeTranslator = ({ onClose }: MorseCodeTranslatorProps) => {
  const [textInput, setTextInput] = useState('');
  const [morseInput, setMorseInput] = useState('');
  const [textToMorse, setTextToMorse] = useState('');
  const [morseToText, setMorseToText] = useState('');

  // Complete and accurate Morse code mapping
  const morseCodeMap: { [key: string]: string } = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..', 
    '0': '-----', '1': '.----', '2': '..---', '3': '...--', '4': '....-', 
    '5': '.....', '6': '-....', '7': '--...', '8': '---..', '9': '----.', 
    ' ': '/', '.': '.-.-.-', ',': '--..--', '?': '..--..', "'": '.----.',
    '!': '-.-.--', '/': '-..-.', '(': '-.--.', ')': '-.--.-', '&': '.-...',
    ':': '---...', ';': '-.-.-.', '=': '-...-', '+': '.-.-.', '-': '-....-',
    '_': '..--.-', '"': '.-..-.', '$': '...-..-', '@': '.--.-.'
  };

  // Reverse mapping for Morse to text conversion
  const reverseMorseMap = Object.fromEntries(
    Object.entries(morseCodeMap).map(([key, value]) => [value, key])
  );

  // Convert text to Morse code
  const textToMorseCode = (text: string): string => {
    if (!text.trim()) return '';
    
    return text
      .toUpperCase()
      .split('')
      .map(char => {
        if (char === ' ') return '/';
        return morseCodeMap[char] || char;
      })
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
  };

  // Convert Morse code to text
  const morseCodeToText = (morse: string): string => {
    if (!morse.trim()) return '';
    
    return morse
      .trim()
      .split(' ')
      .map(code => {
        if (code === '/') return ' ';
        return reverseMorseMap[code] || code;
      })
      .join('')
      .replace(/\s+/g, ' ')
      .trim();
  };

  const handleTextChange = (value: string) => {
    setTextInput(value);
    setTextToMorse(textToMorseCode(value));
  };

  const handleMorseChange = (value: string) => {
    setMorseInput(value);
    setMorseToText(morseCodeToText(value));
  };

  // Morse code chart data for display
  const morseChartData = [
    { letter: 'A', code: '.-' }, { letter: 'B', code: '-...' }, { letter: 'C', code: '-.-.' },
    { letter: 'D', code: '-..' }, { letter: 'E', code: '.' }, { letter: 'F', code: '..-.' },
    { letter: 'G', code: '--.' }, { letter: 'H', code: '....' }, { letter: 'I', code: '..' },
    { letter: 'J', code: '.---' }, { letter: 'K', code: '-.-' }, { letter: 'L', code: '.-..' },
    { letter: 'M', code: '--' }, { letter: 'N', code: '-.' }, { letter: 'O', code: '---' },
    { letter: 'P', code: '.--.' }, { letter: 'Q', code: '--.-' }, { letter: 'R', code: '.-.' },
    { letter: 'S', code: '...' }, { letter: 'T', code: '-' }, { letter: 'U', code: '..-' },
    { letter: 'V', code: '...-' }, { letter: 'W', code: '.--' }, { letter: 'X', code: '-..-' },
    { letter: 'Y', code: '-.--' }, { letter: 'Z', code: '--..' }
  ];

  return (
    <Card className="w-full max-w-5xl max-h-[90vh] bg-white shadow-2xl border border-gray-200 rounded-lg overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardTitle className="text-xl flex items-center space-x-2 font-semibold">
          <span>📡</span>
          <span>Morse Code Central</span>
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="w-8 h-8 p-0 hover:bg-white/20 text-white hover:text-white"
        >
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      <CardContent className="p-6 space-y-6 overflow-y-auto max-h-[80vh]">
        {/* Why Morse Code Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-gray-800 mb-2 flex items-center space-x-2">
            <span>🤔</span>
            <span>Why Morse Code?</span>
          </h3>
          <p className="text-sm text-gray-600">
            All communications in ChaosBird are done in Morse code for mystery, fun, 
            and a layer of safety! It adds an educational twist and makes every 
            message feel like a secret code between digital friends.
          </p>
        </div>

        {/* Text to Morse and Morse to Text in a horizontal layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Text to Morse */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-800 flex items-center space-x-2">
              <span>📝</span>
              <span>Text → Morse</span>
            </h3>
            <div className="space-y-2">
              <label className="text-sm text-gray-600">Enter text:</label>
              <Input
                value={textInput}
                onChange={(e) => handleTextChange(e.target.value)}
                placeholder="Hello world"
                className="text-sm"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm text-gray-600">Morse code:</label>
              <Textarea
                value={textToMorse}
                readOnly
                placeholder="Morse code will appear here..."
                className="text-sm bg-gray-50 min-h-[80px] font-mono resize-none"
              />
            </div>
          </div>

          {/* Morse to Text */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-800 flex items-center space-x-2">
              <span>📡</span>
              <span>Morse → Text</span>
            </h3>
            <div className="space-y-2">
              <label className="text-sm text-gray-600">Enter morse code:</label>
              <Textarea
                value={morseInput}
                onChange={(e) => handleMorseChange(e.target.value)}
                placeholder=".... . .-.. .-.. --- / .-- --- .-. .-.. -.."
                className="text-sm font-mono min-h-[60px] resize-none"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm text-gray-600">Decoded text:</label>
              <Input
                value={morseToText}
                readOnly
                placeholder="Decoded text will appear here..."
                className="text-sm bg-gray-50"
              />
            </div>
          </div>
        </div>

        {/* Morse Code Chart */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-800 flex items-center space-x-2">
            <span>📊</span>
            <span>Morse Code Chart</span>
          </h4>
          <div className="grid grid-cols-6 md:grid-cols-8 lg:grid-cols-13 gap-2 text-xs">
            {morseChartData.map(({ letter, code }) => (
              <div key={letter} className="text-center p-2 bg-gray-50 rounded border hover:bg-blue-50 transition-colors">
                <div className="font-bold text-gray-800">{letter}</div>
                <div className="text-gray-600 font-mono text-xs">{code}</div>
              </div>
            ))}
          </div>
          <div className="text-xs text-gray-500 bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <p><strong>Note:</strong> Use spaces to separate letters, and "/" for word breaks.</p>
            <p><strong>Example:</strong> "BIRD" = "-... .. .-. -.."</p>
          </div>
        </div>

        {/* Fun Messages to Try */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-800 flex items-center space-x-2">
            <span>🎉</span>
            <span>Fun Messages to Try</span>
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="bg-blue-50 p-3 rounded border border-blue-200">
              <strong>"HELLO":</strong> <span className="font-mono">.... . .-.. .-.. ---</span>
            </div>
            <div className="bg-green-50 p-3 rounded border border-green-200">
              <strong>"BIRD":</strong> <span className="font-mono">-... .. .-. -..</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MorseCodeTranslator;
