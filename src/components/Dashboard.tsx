
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import MorseCodeTranslator from './MorseCodeTranslator';
import SendMessageForm from './SendMessageForm';
import MessagesList from './MessagesList';
import GroupSearch from './GroupSearch';
import GroupMessageForm from './GroupMessageForm';
import GroupChatWindow from './GroupChatWindow';
import { useDatabaseContext, DatabaseProvider, Group } from '@/hooks/useDatabase';

interface DashboardProps {
  username: string;
  onSwitchUser: () => void;
}

const DashboardContent = ({ username, onSwitchUser }: DashboardProps) => {
  const [showMorseTranslator, setShowMorseTranslator] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [stats, setStats] = useState({
    totalFlippers: 0,
    birdsGiven: 0,
    todaysBirds: 0
  });
  const [userInitialized, setUserInitialized] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [showGroupChat, setShowGroupChat] = useState(false);
  const { createOrGetUser, loading, getAllUsers, getMessages, currentUser } = useDatabaseContext();

  // Monitor currentUser changes
  useEffect(() => {
    if (currentUser && !userInitialized) {
      setUserInitialized(true);
    }
  }, [currentUser, userInitialized]);

  useEffect(() => {
    const initializeUser = async () => {
      console.log('=== Dashboard: Starting user initialization for:', username);
      console.log('=== Dashboard: Current user state:', currentUser);
      console.log('=== Dashboard: User initialized state:', userInitialized);

      if (!userInitialized && !loading) {
        const user = await createOrGetUser(username);
        console.log('=== Dashboard: createOrGetUser returned:', user);

        if (user) {
          console.log('=== Dashboard: User created/found successfully, setting initialized to true');
          setUserInitialized(true);
          await loadStats();
        } else {
          console.error('=== Dashboard: Failed to create/get user');
        }
      }
    };

    initializeUser();
  }, [username, userInitialized, loading, createOrGetUser]);

  const loadStats = async () => {
    try {
      const allUsers = await getAllUsers();
      const allMessages = await getMessages();
      
      const userSentMessages = allMessages.filter(msg => 
        msg.sender?.username.toLowerCase() === username.toLowerCase()
      );
      
      const today = new Date();
      const todayMessages = userSentMessages.filter(msg => {
        const msgDate = new Date(msg.created_at);
        return msgDate.toDateString() === today.toDateString();
      });
      
      setStats({
        totalFlippers: allUsers.length,
        birdsGiven: userSentMessages.length,
        todaysBirds: todayMessages.length
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleMessageSent = () => {
    setRefreshTrigger(prev => prev + 1);
    loadStats();
  };

  const handleGroupSelected = (group: Group) => {
    setSelectedGroup(group);
    setShowGroupChat(true);
  };

  const handleBackFromChat = () => {
    setShowGroupChat(false);
    setSelectedGroup(null);
  };

  const handleGroupMessageSent = () => {
    setRefreshTrigger(prev => prev + 1);
    loadStats();
  };



  // Don't render SendMessageForm until user is initialized
  if (!userInitialized || !currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 text-lg">Initializing your account...</p>
        </div>
      </div>
    );
  }

  // Show group chat window if a group is selected
  if (showGroupChat && selectedGroup) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-4">
        <div className="max-w-4xl mx-auto h-[calc(100vh-2rem)]">
          <GroupChatWindow
            group={selectedGroup}
            onBack={handleBackFromChat}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Header Bar */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">CB</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                ChaosBird
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-600">Welcome, <strong>{username}</strong></span>
              <Button
                onClick={onSwitchUser}
                variant="outline"
                size="sm"
                className="text-slate-600 border-slate-300 hover:bg-slate-50"
              >
                Switch User
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8 space-y-8">
        {/* Stats Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-white/60 backdrop-blur-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">👥</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Users</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totalFlippers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">📤</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">Messages Sent</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.birdsGiven}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">🚀</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600">Today's Messages</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.todaysBirds}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Individual Message Form */}
        <SendMessageForm onMessageSent={handleMessageSent} />

        {/* Group Search and Messaging */}
        <div className="grid md:grid-cols-2 gap-6">
          <GroupSearch onGroupSelected={handleGroupSelected} />
          {selectedGroup && (
            <GroupMessageForm
              group={selectedGroup}
              onMessageSent={handleGroupMessageSent}
            />
          )}
        </div>

        {/* Messages Display */}
        <MessagesList
          refreshTrigger={refreshTrigger}
          onGroupChatOpen={handleGroupSelected}
        />
      </div>

      {/* Morse Code Translator Button */}
      <div className="fixed bottom-6 right-6 z-20">
        <Button
          onClick={() => setShowMorseTranslator(!showMorseTranslator)}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full w-14 h-14 shadow-xl font-bold text-xl transform hover:scale-105 transition-all duration-300"
          size="lg"
        >
          📡
        </Button>
      </div>
      
      {showMorseTranslator && (
        <MorseCodeTranslator onClose={() => setShowMorseTranslator(false)} />
      )}
    </div>
  );
};

const Dashboard = ({ username, onSwitchUser }: DashboardProps) => {
  return (
    <DatabaseProvider>
      <DashboardContent username={username} onSwitchUser={onSwitchUser} />
    </DatabaseProvider>
  );
};

export default Dashboard;
