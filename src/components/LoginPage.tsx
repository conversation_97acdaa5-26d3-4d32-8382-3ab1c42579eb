
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

interface LoginPageProps {
  onLogin: (username: string) => void;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [username, setUsername] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (username.trim() && /^[a-zA-Z0-9]+$/.test(username.trim())) {
      onLogin(username.trim().toLowerCase());
    }
  };

  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center p-4 relative overflow-hidden">
      {/* Floating Birds */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 text-2xl sm:text-4xl animate-float">🐦</div>
        <div className="absolute top-40 right-20 text-xl sm:text-3xl animate-bounce-slow">🐦</div>
        <div className="absolute bottom-40 left-20 text-2xl sm:text-4xl animate-float" style={{ animationDelay: '2s' }}>🐦</div>
        <div className="absolute top-60 right-1/3 text-xl sm:text-3xl animate-bounce-slow" style={{ animationDelay: '1s' }}>🐦</div>
      </div>

      <div className="max-w-lg w-full space-y-6 sm:space-y-8 relative z-10">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2 sm:space-x-4">
            <span className="text-4xl sm:text-6xl animate-bounce-slow">🐦</span>
            <h1 className="text-4xl sm:text-6xl font-creepster text-gradient-modern animate-pulse-glow">
              ChaosBird
            </h1>
            <span className="text-4xl sm:text-6xl animate-bounce-slow" style={{ animationDelay: '0.5s' }}>🐦</span>
          </div>
          <p className="text-lg sm:text-xl text-gray-600 font-righteous px-4">
            Anonymous Digital Gestures Platform
          </p>
          
          {/* Feature Tags */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mt-4 px-4">
            <span className="glass-effect text-gray-800 px-3 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-gray-200">
              No Passwords
            </span>
            <span className="glass-effect text-gray-800 px-3 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-gray-200">
              Pure Chaos
            </span>
            <span className="glass-effect text-gray-800 px-3 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border border-gray-200">
              Open Book
            </span>
          </div>
        </div>

        {/* Login Card */}
        <Card className="glass-effect shadow-modern border-2 border-gray-200 hover:shadow-card transition-all duration-300">
          <CardContent className="p-6 sm:p-8 text-center space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl sm:text-3xl font-bold flex items-center justify-center space-x-2 font-righteous text-gray-800">
                <span>🎭</span>
                <span>Login with Username</span>
              </h2>
              <p className="text-gray-600 font-medium text-sm sm:text-base font-righteous">
                No passwords needed - just pick any username!
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Input
                  type="text"
                  placeholder="Choose any username..."
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="text-center text-base sm:text-lg py-3 sm:py-4 border-2 border-gray-200 focus:border-primary rounded-lg transition-all duration-300 hover:border-gray-300 glass-effect"
                  pattern="[a-zA-Z0-9]+"
                  title="Only alphanumeric characters allowed"
                />
                <p className="text-xs sm:text-sm text-gray-500 px-2 font-righteous">
                  Pick something fun! (Your identity will be partially masked for privacy)
                </p>
              </div>

              <Button
                type="submit"
                disabled={!username.trim() || !/^[a-zA-Z0-9]+$/.test(username.trim())}
                className="w-full text-lg sm:text-xl py-4 sm:py-6 bg-gradient-to-r from-primary via-purple-600 to-pink-600 hover:from-primary/90 hover:via-purple-600/90 hover:to-pink-600/90 text-white font-bold font-righteous rounded-lg shadow-modern transform hover:scale-105 transition-all duration-300 disabled:transform-none disabled:opacity-50"
              >
                🚀 Enter ChaosBird
              </Button>
            </form>

            <p className="text-gray-500 text-xs sm:text-sm font-righteous">
              Ready to spread some anonymous digital mischief? 😈
            </p>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-gray-600 text-xs sm:text-sm px-4 font-righteous">
            Remember: Keep it fun, anonymous, and safe! 🛡️
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
