import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Send, Users, UserPlus } from 'lucide-react';
import { Group, Message, useDatabaseContext } from '@/hooks/useDatabase';

// Simple Morse code converter
const textToMorse = (text: string): string => {
  const morseCode: { [key: string]: string } = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
    '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
    '8': '---..', '9': '----.', ' ': '/'
  };

  return text.toUpperCase().split('').map(char => morseCode[char] || char).join(' ');
};

interface GroupChatWindowProps {
  group: Group;
  onBack: () => void;
}

const GroupChatWindow = ({ group, onBack }: GroupChatWindowProps) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [newMemberUsername, setNewMemberUsername] = useState('');
  const [showAddMember, setShowAddMember] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { 
    sendGroupMessage, 
    getMessages, 
    addUserToGroup, 
    currentUser, 
    error 
  } = useDatabaseContext();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load group messages
  const loadMessages = async () => {
    try {
      const allMessages = await getMessages();
      const groupMessages = allMessages.filter(msg => 
        msg.message_type === 'group' && msg.group_id === group.id
      );
      setMessages(groupMessages);
    } catch (err) {
      console.error('Error loading group messages:', err);
    }
  };

  // Load messages on component mount and when refresh trigger changes
  useEffect(() => {
    loadMessages();
  }, [group.id, refreshTrigger]);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Send message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) return;
    
    setLoading(true);
    
    try {
      const morseCode = textToMorse(message.trim());
      const success = await sendGroupMessage(group.id, message.trim(), morseCode);
      
      if (success) {
        setMessage('');
        setRefreshTrigger(prev => prev + 1); // Refresh messages
      }
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setLoading(false);
    }
  };

  // Add member to group
  const handleAddMember = async () => {
    if (!newMemberUsername.trim()) return;
    
    try {
      const success = await addUserToGroup(group.id, newMemberUsername.trim());
      if (success) {
        setNewMemberUsername('');
        setShowAddMember(false);
        // You might want to refresh group info here
      }
    } catch (err) {
      console.error('Error adding member:', err);
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col">
        {/* Header */}
        <CardHeader className="flex-shrink-0 pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onBack}
                className="p-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {group.name}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  {group.member_count} member{group.member_count !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAddMember(!showAddMember)}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Add Member
            </Button>
          </div>
          
          {/* Add Member Section */}
          {showAddMember && (
            <div className="flex gap-2 mt-3 p-3 bg-muted rounded-lg">
              <Input
                placeholder="Enter username to add..."
                value={newMemberUsername}
                onChange={(e) => setNewMemberUsername(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddMember()}
              />
              <Button onClick={handleAddMember} size="sm">
                Add
              </Button>
            </div>
          )}
        </CardHeader>

        {/* Messages Area */}
        <CardContent className="flex-1 flex flex-col min-h-0">
          <div className="flex-1 overflow-y-auto space-y-3 mb-4">
            {messages.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${
                    msg.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'
                  }`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg p-3 ${
                      msg.sender_id === currentUser?.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {msg.sender_id !== currentUser?.id && (
                      <div className="text-xs font-medium mb-1 opacity-70">
                        {msg.sender?.username || 'Unknown User'}
                      </div>
                    )}
                    <div className="text-sm">{msg.message}</div>
                    <div className="text-xs opacity-70 mt-1">
                      {formatTime(msg.created_at)}
                    </div>
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Input
              placeholder="Type your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              disabled={loading}
              className="flex-1"
            />
            <Button 
              type="submit" 
              disabled={loading || !message.trim()}
              size="sm"
              className="px-3"
            >
              <Send className="h-4 w-4" />
            </Button>
          </form>
          
          {error && (
            <div className="text-sm text-destructive mt-2">
              {error}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GroupChatWindow;
