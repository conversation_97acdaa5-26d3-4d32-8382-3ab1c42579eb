
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDatabaseContext, User } from '@/hooks/useDatabase';
import { useToast } from '@/hooks/use-toast';
import { Send, User as UserIcon, MessageCircle } from 'lucide-react';
import UserCreationOverlay from './UserCreationOverlay';
import { validateAndFormatName } from '@/utils/nameValidation';

interface SendMessageFormProps {
  onMessageSent?: () => void;
}

const SendMessageForm = ({ onMessageSent }: SendMessageFormProps) => {
  const [recipient, setRecipient] = useState('');
  const [message, setMessage] = useState('');
  const [validationMessage, setValidationMessage] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const { sendMessage, loading, currentUser, error } = useDatabaseContext();
  const { toast } = useToast();

  console.log('SendMessageForm - Current user:', currentUser);
  console.log('SendMessageForm - Database error:', error);

  const textToMorse = (text: string): string => {
    const morseCode: { [key: string]: string } = {
      'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
      'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
      'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
      'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
      'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
      '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
      '8': '---..', '9': '----.', ' ': '/'
    };

    return text.toUpperCase().split('').map(char => morseCode[char] || char).join(' ');
  };

  const validateRecipient = async (username: string) => {
    if (!username.trim()) {
      setValidationMessage('');
      return;
    }

    setIsValidating(true);
    try {
      // Validate username format
      const validation = validateAndFormatName(username);
      if (!validation.isValid) {
        setValidationMessage(`❌ ${validation.error}`);
      } else if (validation.formattedName !== username.trim()) {
        setValidationMessage(`✓ Will be formatted as: ${validation.formattedName}`);
      } else {
        setValidationMessage('✓ Ready to send message');
      }
    } catch (error) {
      console.error('Error validating recipient:', error);
      setValidationMessage('❌ Error validating username');
    } finally {
      setIsValidating(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (recipient) {
        validateRecipient(recipient);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [recipient]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!recipient || !message.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter both a recipient and a message",
        variant: "destructive"
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Authentication Error",
        description: "No current user found. Please refresh and try again.",
        variant: "destructive"
      });
      return;
    }

    const morseCode = textToMorse(message);

    console.log('=== FORM SUBMIT ===');
    console.log('Current user:', currentUser);
    console.log('Recipient:', recipient);
    console.log('Message:', message);

    // Simple: just send the message
    const success = await sendMessage(recipient.toLowerCase(), message, morseCode);

    if (success) {
      toast({
        title: "Message Sent",
        description: `Your message was delivered to ${recipient}`,
      });
      setMessage('');
      setRecipient('');
      setValidationMessage('');
      onMessageSent?.();
    } else {
      toast({
        title: "Send Failed",
        description: "Failed to send message. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border border-slate-200 shadow-lg hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-6">
          <CardTitle className="text-xl font-semibold text-slate-800 flex items-center space-x-2">
            <MessageCircle className="w-5 h-5 text-blue-600" />
            <span>Send Message</span>
          </CardTitle>
          <p className="text-sm text-slate-600">Send anonymous messages through Morse code</p>
        </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-700 flex items-center space-x-2">
              <UserIcon className="w-4 h-4" />
              <span>Recipient Username</span>
            </label>
            <Input
              value={recipient}
              onChange={(e) => setRecipient(e.target.value)}
              placeholder="Enter username..."
              className="bg-white border-slate-300 focus:border-blue-500 focus:ring-blue-500/20"
            />
            {(validationMessage || isValidating) && (
              <div className={`text-xs px-3 py-2 rounded-md transition-all duration-300 ${
                validationMessage.includes('found') 
                  ? 'bg-green-50 text-green-700 border border-green-200' 
                  : validationMessage.includes('created')
                  ? 'bg-amber-50 text-amber-700 border border-amber-200'
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}>
                {isValidating ? 'Checking username...' : validationMessage}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-700 flex items-center space-x-2">
              <MessageCircle className="w-4 h-4" />
              <span>Your Message</span>
            </label>
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message..."
              maxLength={200}
              className="bg-white border-slate-300 focus:border-blue-500 focus:ring-blue-500/20"
            />
            <div className="flex justify-between text-xs text-slate-500">
              <span>{message.length}/200 characters</span>
              <span className="text-blue-600">Auto-converted to Morse</span>
            </div>
            {message && (
              <div className="mt-3 p-3 bg-slate-50 rounded-lg border border-slate-200">
                <div className="text-xs font-medium text-slate-600 mb-2 flex items-center space-x-1">
                  <span>📡</span>
                  <span>Morse Code Preview:</span>
                </div>
                <div className="font-mono text-sm text-slate-700 break-all bg-white p-2 rounded border">
                  {textToMorse(message)}
                </div>
              </div>
            )}
          </div>

          <Button
            type="submit"
            disabled={loading || !recipient || !message.trim()}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2 h-11 rounded-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 disabled:transform-none disabled:opacity-50"
          >
            {loading ? (
              <span className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending...</span>
              </span>
            ) : (
              <span className="flex items-center justify-center space-x-2">
                <Send className="w-4 h-4" />
                <span>Send Message</span>
              </span>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default SendMessageForm;
