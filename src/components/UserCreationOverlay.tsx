import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface UserCreationOverlayProps {
  username: string;
  isVisible: boolean;
}

const UserCreationOverlay = ({ username, isVisible }: UserCreationOverlayProps) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <Card className="bg-white/90 backdrop-blur-sm border border-slate-200 shadow-2xl max-w-md mx-4">
        <CardContent className="p-8 text-center space-y-6">
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
            
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-slate-800">Creating New User</h2>
              <p className="text-slate-600">
                No existing user found for <strong>"{username}"</strong>
              </p>
              <p className="text-slate-500 text-sm">
                Creating new user account, please wait...
              </p>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-2 text-slate-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserCreationOverlay;
