
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Lock, Eye, MessageSquare, Users, Zap } from 'lucide-react';

interface LandingPageProps {
  onContinue: () => void;
}

const LandingPage = ({ onContinue }: LandingPageProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col items-center justify-center p-6 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] opacity-50"></div>
      
      <div className="max-w-4xl w-full space-y-8 relative z-10">
        {/* Header Section */}
        <div className="text-center space-y-6">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
              ChaosBird
            </h1>
          </div>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Anonymous messaging platform with Morse code encryption
          </p>
          <div className="flex justify-center space-x-4">
            <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium flex items-center space-x-1">
              <Lock className="w-3 h-3" />
              <span>No Passwords</span>
            </span>
            <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium flex items-center space-x-1">
              <MessageSquare className="w-3 h-3" />
              <span>Morse Code</span>
            </span>
            <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium flex items-center space-x-1">
              <Users className="w-3 h-3" />
              <span>Anonymous</span>
            </span>
          </div>
        </div>

        {/* Policy Section */}
        <Card className="bg-white/80 backdrop-blur-sm shadow-xl border border-slate-200">
          <CardContent className="p-8 space-y-8">
            {/* Important Notice */}
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6">
              <h2 className="text-2xl font-bold text-amber-800 mb-6 flex items-center">
                <Shield className="w-6 h-6 mr-3" />
                Important: Open Platform Policy
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <Lock className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">No Password Protection</p>
                      <p className="text-sm text-amber-700 mt-1">Anyone can access any username</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <Eye className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">Public Messages</p>
                      <p className="text-sm text-amber-700 mt-1">All conversations are visible to everyone</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <Shield className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">Content Monitoring</p>
                      <p className="text-sm text-amber-700 mt-1">All messages are automatically scanned</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <MessageSquare className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">Morse Code Only</p>
                      <p className="text-sm text-amber-700 mt-1">All messages converted to Morse code</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <Users className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">Privacy Protection</p>
                      <p className="text-sm text-amber-700 mt-1">Usernames are partially masked</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-white/60 rounded-lg border border-amber-200">
                    <Zap className="w-5 h-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-semibold text-amber-800">No Personal Data</p>
                      <p className="text-sm text-amber-700 mt-1">Never share real names or details</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Example */}
              <div className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-4 mt-6">
                <h3 className="font-semibold text-yellow-800 mb-2 flex items-center">
                  <span className="mr-2">💡</span> Example:
                </h3>
                <p className="text-yellow-700 leading-relaxed">
                  If you choose username "johnsmith", anyone can later enter "johnsmith" 
                  to view your entire message history. Choose wisely!
                </p>
              </div>
            </div>

            {/* Best Use Cases */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
              <h3 className="font-semibold text-green-800 mb-4 flex items-center text-lg">
                <span className="mr-2">✅</span> Perfect for:
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center text-green-700 p-2 bg-white/50 rounded-lg">
                    <span className="mr-3">🎭</span> Anonymous communication
                  </div>
                  <div className="flex items-center text-green-700 p-2 bg-white/50 rounded-lg">
                    <span className="mr-3">📡</span> Learning Morse code
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center text-green-700 p-2 bg-white/50 rounded-lg">
                    <span className="mr-3">🤝</span> Temporary connections
                  </div>
                  <div className="flex items-center text-green-700 p-2 bg-white/50 rounded-lg">
                    <span className="mr-3">🎮</span> Digital experiments
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="text-center space-y-6">
          <Button 
            onClick={onContinue}
            size="lg"
            className="text-lg px-12 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 h-auto"
          >
            <span className="flex items-center justify-center space-x-2">
              <MessageSquare className="w-5 h-5" />
              <span>Continue to ChaosBird</span>
            </span>
          </Button>
          
          <p className="text-sm text-slate-500 max-w-2xl mx-auto">
            By continuing, you acknowledge this is an open platform with no password protection or data privacy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
