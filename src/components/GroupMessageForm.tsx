import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDatabaseContext, Group } from '@/hooks/useDatabase';
import { Send, MessageSquare } from 'lucide-react';

// Simple Morse code converter
const textToMorse = (text: string): string => {
  const morseCode: { [key: string]: string } = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
    '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
    '8': '---..', '9': '----.', ' ': '/'
  };

  return text.toUpperCase().split('').map(char => morseCode[char] || char).join(' ');
};

interface GroupMessageFormProps {
  group: Group;
  onMessageSent: () => void;
}

const GroupMessageForm = ({ group, onMessageSent }: GroupMessageFormProps) => {
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const { sendGroupMessage, error } = useDatabaseContext();



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) return;
    
    setLoading(true);
    
    try {
      const morseCode = textToMorse(message.trim());
      const success = await sendGroupMessage(group.id, message.trim(), morseCode);
      
      if (success) {
        setMessage('');
        onMessageSent();
      }
    } catch (err) {
      console.error('Error sending group message:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-purple-600" />
          <span>Send to Group: {group.name}</span>
          <span className="text-sm text-purple-600 font-normal">
            ({group.member_count} member{group.member_count !== 1 ? 's' : ''})
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Your Message
            </label>
            <Textarea
              placeholder="Type your message here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="min-h-[100px] resize-none"
              disabled={loading}
            />
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>{message.length}/200 characters</span>
              <span className="text-blue-600">Auto-converted to Morse</span>
            </div>
          </div>

          {error && (
            <div className="p-3 rounded-lg text-sm bg-red-50 text-red-700 border border-red-200">
              ❌ {error}
            </div>
          )}

          <Button
            type="submit"
            disabled={loading || !message.trim()}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium py-3"
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Send className="w-4 h-4" />
                <span>Send Group Message</span>
              </div>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default GroupMessageForm;
