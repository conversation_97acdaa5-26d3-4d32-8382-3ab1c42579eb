
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';
import { useDatabaseContext, Message, Group } from '@/hooks/useDatabase';
import { format } from 'date-fns';

interface MessagesListProps {
  refreshTrigger?: number;
  onGroupChatOpen?: (group: Group) => void;
}

const MessagesList = ({ refreshTrigger, onGroupChatOpen }: MessagesListProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [showMorse, setShowMorse] = useState<{ [key: string]: boolean }>({});
  const { getMessages, markAsRead, currentUser, loading } = useDatabaseContext();

  useEffect(() => {
    loadMessages();
  }, [refreshTrigger]);

  const loadMessages = async () => {
    const fetchedMessages = await getMessages();
    setMessages(fetchedMessages);
  };

  const toggleMorse = (messageId: string) => {
    setShowMorse(prev => ({
      ...prev,
      [messageId]: !prev[messageId]
    }));
  };

  const handleMarkAsRead = async (messageId: string) => {
    const success = await markAsRead(messageId);
    if (success) {
      loadMessages(); // Refresh messages
    }
  };

  const sentMessages = messages.filter(msg =>
    msg.sender?.username === currentUser?.username && msg.message_type === 'individual'
  );
  const receivedMessages = messages.filter(msg =>
    msg.receiver?.username === currentUser?.username && msg.message_type === 'individual'
  );
  const groupMessages = messages.filter(msg => msg.message_type === 'group');

  const MessageCard = ({ message, type }: { message: Message, type: 'sent' | 'received' | 'group' }) => {
    // Determine container styling based on message type and read status
    const getContainerStyle = () => {
      if (type === 'group') {
        // For group messages: purple theme
        return "border rounded-lg p-4 mb-3 bg-purple-50 border-purple-200";
      } else if (type === 'sent') {
        // For sent messages: blue if unread, green if read
        return message.read_at
          ? "border rounded-lg p-4 mb-3 bg-green-50 border-green-200"
          : "border rounded-lg p-4 mb-3 bg-blue-50 border-blue-200";
      } else {
        // For received messages: keep original styling
        return "border rounded-lg p-4 mb-3 bg-white/50";
      }
    };

    return (
      <div key={message.id} className={getContainerStyle()}>
        <div className="flex justify-between items-start mb-2">
          <div>
            <span className="font-medium">
              {type === 'group'
                ? `📢 Group: ${message.group?.name}`
                : type === 'sent'
                  ? `To: ${message.receiver?.username}`
                  : `From: ${message.sender?.username}`
              }
            </span>
            {type === 'group' && onGroupChatOpen && message.group && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onGroupChatOpen({
                  id: message.group_id!,
                  name: message.group?.name || 'Unknown Group',
                  creator_id: '',
                  created_at: '',
                  is_member: true,
                  member_count: 0
                })}
                className="ml-2 flex items-center gap-1"
              >
                <MessageCircle className="h-3 w-3" />
                Open Chat
              </Button>
            )}
            {type === 'group' && (
              <span className="ml-2 bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                From: {message.sender?.username}
              </span>
            )}
            {type === 'received' && !message.read_at && (
              <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">New</span>
            )}
            {type === 'sent' && (
              <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                message.read_at
                  ? 'bg-green-500 text-white'
                  : 'bg-blue-500 text-white'
              }`}>
                {message.read_at ? 'Read' : 'Not Read'}
              </span>
            )}
          </div>
          <span className="text-sm text-gray-500">
            {format(new Date(message.created_at), 'MMM dd, HH:mm')}
          </span>
        </div>

        <div className="mb-2">
          <p className="text-gray-800">{message.message}</p>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => toggleMorse(message.id)}
          >
            {showMorse[message.id] ? 'Hide' : 'Show'} Morse 📡
          </Button>

          {type === 'received' && !message.read_at && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleMarkAsRead(message.id)}
              disabled={loading}
            >
              Mark as Read ✓
            </Button>
          )}
        </div>

        {showMorse[message.id] && (
          <div className="mt-3 p-3 bg-gray-100 rounded font-mono text-sm">
            {message.morse_code}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Individual Messages */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl flex items-center space-x-2">
              <span>📤</span>
              <span>Birds Sent ({sentMessages.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="max-h-96 overflow-y-auto">
            {sentMessages.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No birds sent yet! Start spreading those digital gestures! 🐦
              </p>
            ) : (
              sentMessages.map(message => (
                <MessageCard key={message.id} message={message} type="sent" />
              ))
            )}
          </CardContent>
        </Card>

        <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl flex items-center space-x-2">
              <span>📥</span>
              <span>Birds Received ({receivedMessages.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="max-h-96 overflow-y-auto">
            {receivedMessages.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No birds received yet! Maybe send some first? 😉
              </p>
            ) : (
              receivedMessages.map(message => (
                <MessageCard key={message.id} message={message} type="received" />
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Group Messages */}
      <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center space-x-2">
              <span>📢</span>
              <span>Group Messages ({groupMessages.length})</span>
            </CardTitle>
            {groupMessages.length > 0 && (
              <p className="text-sm text-muted-foreground">
                Click "Open Chat" on any message to join the conversation
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent className="max-h-96 overflow-y-auto">
          {groupMessages.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              No group messages yet! Join or create a group to start chatting! 👥
            </p>
          ) : (
            groupMessages.map(message => (
              <MessageCard key={message.id} message={message} type="group" />
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MessagesList;
