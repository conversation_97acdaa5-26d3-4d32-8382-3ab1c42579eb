import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDatabaseContext, Group } from '@/hooks/useDatabase';
import { Search, Users, Plus, UserPlus } from 'lucide-react';

interface GroupSearchProps {
  onGroupSelected: (group: Group) => void;
}

const GroupSearch = ({ onGroupSelected }: GroupSearchProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentGroup, setCurrentGroup] = useState<Group | null>(null);
  const [newMemberUsername, setNewMemberUsername] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  
  const { searchAndJoinGroup, createGroup, addUserToGroup, error } = useDatabaseContext();

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;
    
    setLoading(true);
    setMessage('');
    
    try {
      // First try to find existing group
      const existingGroup = await searchAndJoinGroup(searchTerm.trim());
      
      if (existingGroup) {

        setCurrentGroup(existingGroup);
        setMessage(`✅ Joined existing group "${existingGroup.name}" with ${existingGroup.member_count} members - Opening chat...`);
        onGroupSelected(existingGroup);
      } else {
        // Group doesn't exist, create new one
        const newGroup = await createGroup(searchTerm.trim());


        if (newGroup) {
          setCurrentGroup(newGroup);
          setMessage(`🎉 Created new group "${newGroup.name}" - you are the first member! Opening chat...`);
          onGroupSelected(newGroup);
        } else {
          setMessage('❌ Failed to create group. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error in group search/create:', err);
      setMessage('❌ An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = async () => {
    if (!currentGroup || !newMemberUsername.trim()) return;
    
    setLoading(true);
    
    try {
      const success = await addUserToGroup(currentGroup.id, newMemberUsername.trim());
      
      if (success) {
        setMessage(`✅ Added ${newMemberUsername} to the group!`);
        setNewMemberUsername('');
        // Update member count
        setCurrentGroup({
          ...currentGroup,
          member_count: (currentGroup.member_count || 0) + 1
        });
      } else {
        setMessage(`❌ Failed to add ${newMemberUsername}. User might not exist.`);
      }
    } catch (err) {
      console.error('Error adding member:', err);
      setMessage('❌ An error occurred while adding member.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: 'search' | 'addMember') => {
    if (e.key === 'Enter') {
      if (action === 'search') {
        handleSearch();
      } else {
        handleAddMember();
      }
    }
  };

  return (
    <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl flex items-center space-x-2">
          <Users className="w-5 h-5 text-purple-600" />
          <span>Group Chat</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Group Search/Create */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Search for group or create new one
          </label>
          <div className="flex space-x-2">
            <Input
              type="text"
              placeholder="Enter group name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, 'search')}
              className="flex-1"
              disabled={loading}
            />
            <Button
              onClick={handleSearch}
              disabled={loading || !searchTerm.trim()}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Search className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Current Group Info */}
        {currentGroup && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-purple-800">📢 {currentGroup.name}</h3>
              <span className="text-sm text-purple-600">
                {currentGroup.member_count} member{currentGroup.member_count !== 1 ? 's' : ''}
              </span>
            </div>
            
            {/* Add Member Section */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-purple-700">
                Add member by username
              </label>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Enter username..."
                  value={newMemberUsername}
                  onChange={(e) => setNewMemberUsername(e.target.value)}
                  onKeyPress={(e) => handleKeyPress(e, 'addMember')}
                  className="flex-1"
                  disabled={loading}
                />
                <Button
                  onClick={handleAddMember}
                  disabled={loading || !newMemberUsername.trim()}
                  variant="outline"
                  size="sm"
                  className="border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                  ) : (
                    <UserPlus className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Status Message */}
        {message && (
          <div className={`p-3 rounded-lg text-sm ${
            message.includes('❌') 
              ? 'bg-red-50 text-red-700 border border-red-200' 
              : 'bg-green-50 text-green-700 border border-green-200'
          }`}>
            {message}
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="p-3 rounded-lg text-sm bg-red-50 text-red-700 border border-red-200">
            ❌ {error}
          </div>
        )}

        {/* Instructions */}
        {!currentGroup && (
          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
            💡 <strong>How it works:</strong> Search for a group name. If it exists, you'll automatically join it. 
            If it doesn't exist, a new group will be created with that name and you'll be the first member.
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GroupSearch;
