
import React, { useState } from 'react';
import LandingPage from '@/components/LandingPage';
import LoginPage from '@/components/LoginPage';
import Dashboard from '@/components/Dashboard';
import UserCreationOverlay from '@/components/UserCreationOverlay';
import { supabase } from '@/integrations/supabase/client';

type AppState = 'landing' | 'login' | 'creating-user' | 'dashboard';

const Index = () => {
  const [currentState, setCurrentState] = useState<AppState>('landing');
  const [username, setUsername] = useState<string>('');
  const [isCreatingUser, setIsCreatingUser] = useState<boolean>(false);

  const handleContinueFromLanding = () => {
    setCurrentState('login');
  };

  const handleLogin = async (user: string) => {
    setUsername(user);

    // Check if user exists
    console.log('=== Index: Checking if user exists:', user);

    try {
      const { data: existingUser, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', user)
        .maybeSingle();

      if (error) {
        console.error('Error checking user:', error);
        // If there's an error, still proceed to dashboard
        setCurrentState('dashboard');
        return;
      }

      if (existingUser) {
        console.log('=== Index: User exists, going to dashboard');
        setCurrentState('dashboard');
      } else {
        console.log('=== Index: User does not exist, showing creation overlay');
        setIsCreatingUser(true);
        setCurrentState('creating-user');

        // Create the user
        await createNewUser(user);
      }
    } catch (err) {
      console.error('Error in handleLogin:', err);
      // If there's an error, proceed to dashboard anyway
      setCurrentState('dashboard');
    }
  };

  const createNewUser = async (user: string) => {
    try {
      console.log('=== Index: Creating new user:', user);

      // Create the user
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([{ username: user }])
        .select()
        .single();

      if (insertError) {
        console.error('Error creating user:', insertError);
        throw insertError;
      }

      console.log('=== Index: User created successfully:', newUser);

      // Wait a moment for better UX
      setTimeout(() => {
        setIsCreatingUser(false);
        setCurrentState('dashboard');
      }, 2000);

    } catch (err) {
      console.error('Error creating user:', err);
      // Even if creation fails, go to dashboard (the dashboard will handle creation)
      setTimeout(() => {
        setIsCreatingUser(false);
        setCurrentState('dashboard');
      }, 1000);
    }
  };

  const handleSwitchUser = () => {
    setUsername('');
    setCurrentState('login');
  };

  const handleBackToLanding = () => {
    setUsername('');
    setCurrentState('landing');
  };

  const renderContent = () => {
    switch (currentState) {
      case 'landing':
        return <LandingPage onContinue={handleContinueFromLanding} />;
      case 'login':
        return <LoginPage onLogin={handleLogin} />;
      case 'creating-user':
        return (
          <>
            <LoginPage onLogin={handleLogin} />
            <UserCreationOverlay username={username} isVisible={isCreatingUser} />
          </>
        );
      case 'dashboard':
        return <Dashboard username={username} onSwitchUser={handleSwitchUser} />;
      default:
        return <LandingPage onContinue={handleContinueFromLanding} />;
    }
  };

  return renderContent();
};

export default Index;
