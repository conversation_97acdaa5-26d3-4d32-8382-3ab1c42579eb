
import { useState, createContext, useContext, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { validateAndFormatName } from '@/utils/nameValidation';

export interface User {
  id: string;
  username: string;
  created_at: string;
  last_active: string | null;
}

export interface Message {
  id: string;
  sender_id: string;
  receiver_id: string | null;
  message: string;
  morse_code: string;
  created_at: string;
  read_at: string | null;
  message_type: 'individual' | 'group';
  group_id: string | null;
  sender?: User;
  receiver?: User;
  group?: Group;
}

export interface Group {
  id: string;
  name: string;
  creator_id: string;
  created_at: string;
  is_member?: boolean;
  member_count?: number;
}

export const useDatabase = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create or get user
  const createOrGetUser = async (username: string): Promise<User | null> => {
    try {
      setLoading(true);
      setError(null);

      console.log('=== Starting createOrGetUser for:', username);

      // Validate and format the username
      const validation = validateAndFormatName(username);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid username');
        return null;
      }

      const formattedUsername = validation.formattedName;
      console.log('Formatted username:', formattedUsername);

      // First try to get existing user
      console.log('Checking for existing user:', formattedUsername);
      const { data: existingUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('username', formattedUsername)
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching user:', fetchError);
        throw fetchError;
      }

      if (existingUser) {
        console.log('Found existing user:', existingUser);
        // Update last_active
        const { error: updateError } = await supabase
          .from('users')
          .update({ last_active: new Date().toISOString() })
          .eq('username', formattedUsername);

        if (updateError) {
          console.error('Error updating last_active:', updateError);
          // Don't throw here, just log the warning
        }

        console.log('Setting currentUser to existing user:', existingUser);
        setCurrentUser(existingUser);
        return existingUser;
      }

      console.log('User not found, creating new user:', formattedUsername);
      // Create new user if doesn't exist
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([{ username: formattedUsername }])
        .select()
        .single();

      if (insertError) {
        console.error('Error creating user:', insertError);
        throw insertError;
      }

      console.log('Successfully created new user:', newUser);
      console.log('Setting currentUser to new user:', newUser);
      setCurrentUser(newUser);
      return newUser;
    } catch (err: any) {
      console.error('=== Error in createOrGetUser:', err);
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Send a message - SIMPLE VERSION
  const sendMessage = async (receiverUsername: string, message: string, morseCode: string): Promise<boolean> => {
    console.log('=== SIMPLE SEND MESSAGE ===');
    console.log('Current user:', currentUser);
    console.log('Receiver username:', receiverUsername);
    console.log('Message:', message);

    // Step 1: Ensure we have a current user
    if (!currentUser) {
      console.error('NO CURRENT USER!');
      setError('No current user found. Please refresh and try again.');
      return false;
    }

    try {
      setError(null);

      // Validate and format receiver username
      const validation = validateAndFormatName(receiverUsername);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid receiver username');
        return false;
      }

      const formattedReceiverUsername = validation.formattedName;

      // Step 2: Check if receiver exists, if not create them
      console.log('Checking if receiver exists...');
      let { data: receiverUser, error: findError } = await supabase
        .from('users')
        .select('*')
        .eq('username', formattedReceiverUsername)
        .maybeSingle();

      if (findError) {
        console.error('Error finding receiver:', findError);
        setError('Error finding receiver user');
        return false;
      }

      if (!receiverUser) {
        console.log('Receiver does not exist, creating...');
        const { data: newReceiver, error: createError } = await supabase
          .from('users')
          .insert([{ username: formattedReceiverUsername }])
          .select()
          .single();

        if (createError) {
          console.error('Failed to create receiver:', createError);
          setError('Failed to create receiver user');
          return false;
        }
        receiverUser = newReceiver;
        console.log('Created receiver:', receiverUser);
      } else {
        console.log('Found existing receiver:', receiverUser);
      }

      // Step 3: Insert message
      console.log('Inserting message...');
      const { data: messageData, error: messageError } = await (supabase as any)
        .from('messages')
        .insert([{
          sender_id: currentUser.id,
          receiver_id: receiverUser.id,
          message: message,
          morse_code: morseCode,
          message_type: 'individual'
        }])
        .select()
        .single();

      if (messageError) {
        console.error('Failed to insert message:', messageError);
        setError('Failed to send message');
        return false;
      }

      console.log('Message sent successfully:', messageData);
      return true;

    } catch (error: any) {
      console.error('Error in sendMessage:', error);
      setError(error.message);
      return false;
    }
  };

  // Get messages (sent and received)
  const getMessages = async (): Promise<Message[]> => {
    try {
      setLoading(true);
      setError(null);

      if (!currentUser) {
        console.log('No current user for getting messages');
        return [];
      }

      console.log('Getting messages for user:', currentUser.username);

      // Get individual messages where current user is sender or receiver
      const { data: individualMessages, error: individualError } = await (supabase as any)
        .from('messages')
        .select(`
          *,
          sender:users!messages_sender_id_fkey (
            id,
            username,
            created_at,
            last_active
          ),
          receiver:users!messages_receiver_id_fkey (
            id,
            username,
            created_at,
            last_active
          )
        `)
        .eq('message_type', 'individual')
        .or(`sender_id.eq.${currentUser.id},receiver_id.eq.${currentUser.id}`)
        .order('created_at', { ascending: false });

      if (individualError) {
        console.error('Individual messages fetch error:', individualError);
        throw individualError;
      }

      // Get group messages where current user is a member
      const { data: userGroups, error: groupsError } = await (supabase as any)
        .from('group_members')
        .select('group_id')
        .eq('user_id', currentUser.id);

      if (groupsError) {
        console.error('User groups fetch error:', groupsError);
        throw groupsError;
      }

      let groupMessages: any[] = [];
      if (userGroups && userGroups.length > 0) {
        const groupIds = userGroups.map((g: any) => g.group_id);

        const { data: fetchedGroupMessages, error: groupMessagesError } = await (supabase as any)
          .from('messages')
          .select(`
            *,
            sender:users!messages_sender_id_fkey (
              id,
              username,
              created_at,
              last_active
            ),
            group:groups!messages_group_id_fkey (
              id,
              name,
              creator_id,
              created_at
            )
          `)
          .eq('message_type', 'group')
          .in('group_id', groupIds)
          .order('created_at', { ascending: false });

        if (groupMessagesError) {
          console.error('Group messages fetch error:', groupMessagesError);
          throw groupMessagesError;
        }

        groupMessages = fetchedGroupMessages || [];
      }

      // Combine and sort all messages
      const allMessages = [...(individualMessages || []), ...groupMessages]
        .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      console.log('Individual messages fetched:', individualMessages?.length || 0);
      console.log('Group messages fetched:', groupMessages?.length || 0);
      console.log('Total messages:', allMessages.length);
      return allMessages as unknown as Message[];
    } catch (err: any) {
      console.error('Error in getMessages:', err);
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Mark message as read
  const markAsRead = async (messageId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ read_at: new Date().toISOString() })
        .eq('id', messageId);

      if (error) throw error;
      return true;
    } catch (err: any) {
      console.error('Error marking as read:', err);
      setError(err.message);
      return false;
    }
  };

  // Get all usernames for validation
  const getAllUsers = async (): Promise<User[]> => {
    try {
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .order('username');

      if (error) throw error;
      return users || [];
    } catch (err: any) {
      console.error('Error getting all users:', err);
      setError(err.message);
      return [];
    }
  };

  // Search for group and auto-join if found
  const searchAndJoinGroup = async (groupName: string): Promise<Group | null> => {
    try {
      if (!currentUser) {
        setError('No current user found');
        return null;
      }

      // Validate and format group name
      const validation = validateAndFormatName(groupName);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid group name');
        return null;
      }

      const formattedGroupName = validation.formattedName;

      const { data, error } = await (supabase as any).rpc('auto_join_group', {
        p_group_name: formattedGroupName,
        p_username: currentUser.username
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const result = data[0] as any;
        return {
          id: result.result_group_id,
          name: result.result_group_name,
          creator_id: result.result_creator_id,
          created_at: result.result_created_at,
          is_member: result.result_is_member,
          member_count: result.result_member_count
        } as Group;
      }

      return null;
    } catch (err: any) {
      console.error('Error searching for group:', err);
      setError(err.message);
      return null;
    }
  };

  // Create new group and auto-join creator
  const createGroup = async (groupName: string): Promise<Group | null> => {
    try {
      if (!currentUser) {
        setError('No current user found');
        return null;
      }

      // Validate and format group name
      const validation = validateAndFormatName(groupName);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid group name');
        return null;
      }

      const formattedGroupName = validation.formattedName;

      const { data, error } = await (supabase as any).rpc('create_group_and_join', {
        p_group_name: formattedGroupName,
        p_username: currentUser.username
      });



      if (error) throw error;

      if (data && data.length > 0) {
        const result = data[0] as any;
        return {
          id: result.result_group_id,
          name: result.result_group_name,
          creator_id: result.result_creator_id,
          created_at: result.result_created_at,
          is_member: result.result_is_member,
          member_count: result.result_member_count
        } as Group;
      }

      return null;
    } catch (err: any) {
      console.error('Error creating group:', err);
      setError(err.message);
      return null;
    }
  };

  // Add user to group by username
  const addUserToGroup = async (groupId: string, username: string): Promise<boolean> => {
    try {
      if (!groupId) {
        throw new Error('Group ID is required');
      }

      if (!username) {
        throw new Error('Username is required');
      }

      // Validate and format username
      const validation = validateAndFormatName(username);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid username');
      }

      const formattedUsername = validation.formattedName;

      const { data, error } = await (supabase as any).rpc('add_user_to_group', {
        p_group_id: groupId,
        p_username: formattedUsername
      });

      if (error) {
        console.error('RPC error details:', error);
        throw error;
      }
      return data || false;
    } catch (err: any) {
      console.error('Error adding user to group:', err);
      setError(err.message);
      return false;
    }
  };

  // Send group message
  const sendGroupMessage = async (groupId: string, message: string, morseCode: string): Promise<boolean> => {
    try {
      if (!currentUser) {
        setError('No current user found');
        return false;
      }

      if (!groupId) {
        setError('Group ID is required');
        return false;
      }

      const { data: messageData, error: messageError } = await (supabase as any)
        .from('messages')
        .insert([{
          sender_id: currentUser.id,
          group_id: groupId,
          message: message,
          morse_code: morseCode,
          message_type: 'group'
        }])
        .select()
        .single();

      if (messageError) {
        console.error('Failed to send group message:', messageError);
        setError('Failed to send group message');
        return false;
      }

      console.log('Group message sent successfully:', messageData);
      return true;
    } catch (error: any) {
      console.error('Error in sendGroupMessage:', error);
      setError(error.message);
      return false;
    }
  };

  return {
    currentUser,
    loading,
    error,
    createOrGetUser,
    sendMessage,
    getMessages,
    markAsRead,
    getAllUsers,
    searchAndJoinGroup,
    createGroup,
    addUserToGroup,
    sendGroupMessage
  };
};

// Create a context for sharing database state
interface DatabaseContextType {
  currentUser: User | null;
  loading: boolean;
  error: string | null;
  createOrGetUser: (username: string) => Promise<User | null>;
  sendMessage: (receiverUsername: string, message: string, morseCode: string) => Promise<boolean>;
  getMessages: () => Promise<Message[]>;
  markAsRead: (messageId: string) => Promise<boolean>;
  getAllUsers: () => Promise<User[]>;
  searchAndJoinGroup: (groupName: string) => Promise<Group | null>;
  createGroup: (groupName: string) => Promise<Group | null>;
  addUserToGroup: (groupId: string, username: string) => Promise<boolean>;
  sendGroupMessage: (groupId: string, message: string, morseCode: string) => Promise<boolean>;
}

const DatabaseContext = createContext<DatabaseContextType | null>(null);

// Provider component
export const DatabaseProvider = ({ children }: { children: ReactNode }) => {
  const databaseState = useDatabase();

  return (
    <DatabaseContext.Provider value={databaseState}>
      {children}
    </DatabaseContext.Provider>
  );
};

// Hook to use the shared database context
export const useDatabaseContext = () => {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error('useDatabaseContext must be used within a DatabaseProvider');
  }
  return context;
};
