-- Fix for add_user_to_group function
-- Run this in Supabase Dashboard > SQL Editor

-- Update the add_user_to_group function to create users if they don't exist
CREATE OR REPLACE FUNCTION add_user_to_group(p_group_id UUID, p_username TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get or create user
  SELECT id INTO v_user_id FROM users WHERE username = p_username;
  
  IF v_user_id IS NULL THEN
    -- Create user if doesn't exist
    INSERT INTO users (username)
    VALUES (p_username)
    RETURNING id INTO v_user_id;
  END IF;
  
  -- Add user to group
  INSERT INTO group_members (group_id, user_id)
  VALUES (p_group_id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Test the function (optional)
-- SELECT add_user_to_group('your-group-uuid', 'new-username');
