
-- Create users table to store user profiles
CREATE TABLE public.users (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create messages table to store all sent/received messages
CREATE TABLE public.messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  receiver_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  message TEXT NOT NULL,
  morse_code TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Ensure users can't send messages to themselves
  CONSTRAINT different_sender_receiver CHECK (sender_id != receiver_id)
);

-- Add indexes for better query performance
CREATE INDEX idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX idx_messages_receiver_id ON public.messages(receiver_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX idx_users_username ON public.users(username);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
-- Users can view all usernames (for sending messages)
CREATE POLICY "Anyone can view usernames" 
  ON public.users 
  FOR SELECT 
  USING (true);

-- Users can insert their own profile
CREATE POLICY "Users can create their own profile" 
  ON public.users 
  FOR INSERT 
  WITH CHECK (true);

-- Users can update any profile (since we're using anon key)
CREATE POLICY "Users can update profiles" 
  ON public.users 
  FOR UPDATE 
  USING (true);

-- RLS Policies for messages table
-- Users can view all messages (we'll filter in application logic)
CREATE POLICY "Users can view messages" 
  ON public.messages 
  FOR SELECT 
  USING (true);

-- Users can send any message
CREATE POLICY "Users can send messages" 
  ON public.messages 
  FOR INSERT 
  WITH CHECK (true);

-- Users can update any message
CREATE POLICY "Users can update messages" 
  ON public.messages 
  FOR UPDATE 
  USING (true);

-- Disable RLS for development (simpler approach)
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages DISABLE ROW LEVEL SECURITY;
