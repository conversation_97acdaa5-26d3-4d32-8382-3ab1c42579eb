-- Migration: Fix Group Functions
-- Date: 2025-08-02
-- Description: Fix ambiguous column reference errors in group functions

-- Drop and recreate the auto_join_group function with proper table aliases
DROP FUNCTION IF EXISTS auto_join_group(TEXT, TEXT);
CREATE OR REPLACE FUNCTION auto_join_group(group_name_param TEXT, username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member B<PERSON><PERSON><PERSON>N,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_group_id UUID;
  found_user_id UUID;
  group_record RECORD;
BEGIN
  -- Get user ID
  SELECT u.id INTO found_user_id 
  FROM public.users u
  WHERE u.username = username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', username_param;
  END IF;
  
  -- Find group by name (case-insensitive)
  SELECT g.id, g.name, g.creator_id, g.created_at
  INTO group_record
  FROM public.groups g
  WHERE LOWER(g.name) = LOWER(group_name_param);
  
  IF group_record.id IS NULL THEN
    -- Group doesn't exist, return empty result
    RETURN;
  END IF;
  
  found_group_id := group_record.id;
  
  -- Auto-join user to group if not already a member
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (found_group_id, found_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  -- Return group info with membership status
  RETURN QUERY
  SELECT 
    group_record.id,
    group_record.name,
    group_record.creator_id,
    group_record.created_at,
    TRUE as is_member,
    (SELECT COUNT(*)::BIGINT FROM public.group_members gm WHERE gm.group_id = found_group_id) as member_count;
END;
$$;

-- Drop and recreate the create_group_and_join function with proper table aliases
DROP FUNCTION IF EXISTS create_group_and_join(TEXT, TEXT);
CREATE OR REPLACE FUNCTION create_group_and_join(group_name_param TEXT, creator_username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member BOOLEAN,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_user_id UUID;
  new_group_id UUID;
  new_group_record RECORD;
BEGIN
  -- Get user ID
  SELECT u.id INTO found_user_id 
  FROM public.users u
  WHERE u.username = creator_username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', creator_username_param;
  END IF;
  
  -- Create new group
  INSERT INTO public.groups (name, creator_id)
  VALUES (group_name_param, found_user_id)
  RETURNING id, name, creator_id, created_at INTO new_group_record;
  
  new_group_id := new_group_record.id;
  
  -- Auto-add creator to group
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (new_group_id, found_user_id);
  
  -- Return group info
  RETURN QUERY
  SELECT 
    new_group_record.id,
    new_group_record.name,
    new_group_record.creator_id,
    new_group_record.created_at,
    TRUE as is_member,
    1::BIGINT as member_count;
END;
$$;

-- Drop and recreate the add_user_to_group function with proper error handling
DROP FUNCTION IF EXISTS add_user_to_group(UUID, TEXT);
CREATE OR REPLACE FUNCTION add_user_to_group(group_id_param UUID, username_param TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  found_user_id UUID;
BEGIN
  -- Get user ID
  SELECT u.id INTO found_user_id 
  FROM public.users u
  WHERE u.username = username_param;
  
  IF found_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Add user to group
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (group_id_param, found_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Add helpful comments
COMMENT ON FUNCTION auto_join_group IS 'Searches for a group and auto-joins user if found (fixed ambiguous column references)';
COMMENT ON FUNCTION create_group_and_join IS 'Creates a new group and adds creator as first member (fixed ambiguous column references)';
COMMENT ON FUNCTION add_user_to_group IS 'Adds a user to an existing group by username (with proper error handling)';
