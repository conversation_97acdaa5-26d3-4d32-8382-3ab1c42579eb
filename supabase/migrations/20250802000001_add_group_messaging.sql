-- Migration: Add Group Messaging Support
-- Date: 2025-08-02
-- Description: Adds groups, group_members, group_message_reads tables and updates messages table for group support

-- Create groups table
CREATE TABLE public.groups (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  creator_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create group_members table
CREATE TABLE public.group_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  added_by_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  added_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure a user can only be in a group once
  CONSTRAINT unique_group_member UNIQUE (group_id, user_id)
);

-- Create group_message_reads table for tracking read status in groups
CREATE TABLE public.group_message_reads (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure a user can only mark a message as read once
  CONSTRAINT unique_message_read UNIQUE (message_id, user_id)
);

-- Add group support to existing messages table
ALTER TABLE public.messages 
ADD COLUMN group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE,
ADD COLUMN message_type TEXT NOT NULL DEFAULT 'individual' CHECK (message_type IN ('individual', 'group'));

-- Make receiver_id optional for group messages
ALTER TABLE public.messages 
ALTER COLUMN receiver_id DROP NOT NULL;

-- Drop the old constraint that prevented self-messaging
ALTER TABLE public.messages 
DROP CONSTRAINT IF EXISTS different_sender_receiver;

-- Add new comprehensive constraint for message types and self-messaging prevention
ALTER TABLE public.messages 
ADD CONSTRAINT valid_message_type_and_recipients CHECK (
  (message_type = 'individual' AND receiver_id IS NOT NULL AND group_id IS NULL AND sender_id != receiver_id) OR
  (message_type = 'group' AND receiver_id IS NULL AND group_id IS NOT NULL)
);

-- Add indexes for better query performance
CREATE UNIQUE INDEX idx_groups_name_unique ON public.groups(LOWER(name));
CREATE INDEX idx_groups_creator_id ON public.groups(creator_id);
CREATE INDEX idx_groups_created_at ON public.groups(created_at DESC);

CREATE INDEX idx_group_members_group_id ON public.group_members(group_id);
CREATE INDEX idx_group_members_user_id ON public.group_members(user_id);
CREATE INDEX idx_group_members_added_at ON public.group_members(added_at DESC);

CREATE INDEX idx_group_message_reads_message_id ON public.group_message_reads(message_id);
CREATE INDEX idx_group_message_reads_user_id ON public.group_message_reads(user_id);

CREATE INDEX idx_messages_group_id ON public.messages(group_id);
CREATE INDEX idx_messages_message_type ON public.messages(message_type);

-- Disable RLS for development (consistent with existing tables)
ALTER TABLE public.groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_message_reads DISABLE ROW LEVEL SECURITY;

-- Add some helpful comments
COMMENT ON TABLE public.groups IS 'Groups for group messaging functionality';
COMMENT ON TABLE public.group_members IS 'Membership relationships between users and groups';
COMMENT ON TABLE public.group_message_reads IS 'Tracks which users have read which group messages';
COMMENT ON COLUMN public.messages.message_type IS 'Type of message: individual or group';
COMMENT ON COLUMN public.messages.group_id IS 'Group ID for group messages, NULL for individual messages';
