-- Migration: Add Readable Messages View
-- Date: 2025-08-02
-- Description: Creates a view to show usernames alongside UUIDs in messages table for better readability

-- Create a view that shows messages with readable usernames
CREATE OR REPLACE VIEW public.messages_readable AS
SELECT 
  m.id,
  m.sender_id,
  sender.username AS sender_username,
  m.receiver_id,
  receiver.username AS receiver_username,
  m.group_id,
  g.name AS group_name,
  m.message,
  m.morse_code,
  m.message_type,
  m.created_at,
  m.read_at
FROM public.messages m
LEFT JOIN public.users sender ON m.sender_id = sender.id
LEFT JOIN public.users receiver ON m.receiver_id = receiver.id
LEFT JOIN public.groups g ON m.group_id = g.id
ORDER BY m.created_at DESC;

-- Grant access to the view
GRANT SELECT ON public.messages_readable TO postgres;
GRANT SELECT ON public.messages_readable TO anon;
GRANT SELECT ON public.messages_readable TO authenticated;

-- Add comment explaining the view
COMMENT ON VIEW public.messages_readable IS 'Human-readable view of messages showing usernames instead of just UUIDs';

-- Also create a function to get message details with usernames
CREATE OR REPLACE FUNCTION get_message_details(message_id_param UUID)
RETURNS TABLE(
  id UUID,
  sender_id UUID,
  sender_username TEXT,
  receiver_id UUID,
  receiver_username TEXT,
  group_id UUID,
  group_name TEXT,
  message TEXT,
  morse_code TEXT,
  message_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id,
    m.sender_id,
    sender.username AS sender_username,
    m.receiver_id,
    receiver.username AS receiver_username,
    m.group_id,
    g.name AS group_name,
    m.message,
    m.morse_code,
    m.message_type,
    m.created_at,
    m.read_at
  FROM public.messages m
  LEFT JOIN public.users sender ON m.sender_id = sender.id
  LEFT JOIN public.users receiver ON m.receiver_id = receiver.id
  LEFT JOIN public.groups g ON m.group_id = g.id
  WHERE m.id = message_id_param;
END;
$$;

-- Add comment for the function
COMMENT ON FUNCTION get_message_details IS 'Returns detailed message information with usernames for a specific message ID';
