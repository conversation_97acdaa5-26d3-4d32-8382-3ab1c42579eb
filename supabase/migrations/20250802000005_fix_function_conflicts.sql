-- Migration: Fix Function Parameter Conflicts
-- Date: 2025-08-02
-- Description: Fix parameter name conflicts with column names in group functions

-- Drop and recreate the create_group_and_join function with qualified column references
DROP FUNCTION IF EXISTS create_group_and_join(TEXT, TEXT);
CREATE OR REPLACE FUNCTION create_group_and_join(group_name_param TEXT, creator_username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member B<PERSON><PERSON><PERSON><PERSON>,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_user_id UUID;
  new_group_id UUID;
  new_group_name TEXT;
  new_group_creator_id UUID;
  new_group_created_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get user ID
  SELECT u.id INTO found_user_id 
  FROM public.users u
  WHERE u.username = creator_username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', creator_username_param;
  END IF;
  
  -- Create new group
  INSERT INTO public.groups (name, groups.creator_id)
  VALUES (group_name_param, found_user_id)
  RETURNING groups.id, groups.name, groups.creator_id, groups.created_at 
  INTO new_group_id, new_group_name, new_group_creator_id, new_group_created_at;
  
  -- Auto-add creator to group
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (new_group_id, found_user_id);
  
  -- Return group info
  RETURN QUERY
  SELECT 
    new_group_id,
    new_group_name,
    new_group_creator_id,
    new_group_created_at,
    TRUE as is_member,
    1::BIGINT as member_count;
END;
$$;

-- Drop and recreate the auto_join_group function with qualified column references
DROP FUNCTION IF EXISTS auto_join_group(TEXT, TEXT);
CREATE OR REPLACE FUNCTION auto_join_group(group_name_param TEXT, username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member BOOLEAN,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_group_id UUID;
  found_user_id UUID;
  found_group_name TEXT;
  found_group_creator_id UUID;
  found_group_created_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get user ID
  SELECT u.id INTO found_user_id 
  FROM public.users u
  WHERE u.username = username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', username_param;
  END IF;
  
  -- Find group by name (case-insensitive)
  SELECT g.id, g.name, g.creator_id, g.created_at
  INTO found_group_id, found_group_name, found_group_creator_id, found_group_created_at
  FROM public.groups g
  WHERE LOWER(g.name) = LOWER(group_name_param);
  
  IF found_group_id IS NULL THEN
    -- Group doesn't exist, return empty result
    RETURN;
  END IF;
  
  -- Auto-join user to group if not already a member
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (found_group_id, found_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  -- Return group info with membership status
  RETURN QUERY
  SELECT 
    found_group_id,
    found_group_name,
    found_group_creator_id,
    found_group_created_at,
    TRUE as is_member,
    (SELECT COUNT(*)::BIGINT FROM public.group_members gm WHERE gm.group_id = found_group_id) as member_count;
END;
$$;

-- Add helpful comments
COMMENT ON FUNCTION auto_join_group IS 'Searches for a group and auto-joins user if found (fixed parameter conflicts)';
COMMENT ON FUNCTION create_group_and_join IS 'Creates a new group and adds creator as first member (fixed parameter conflicts)';
