-- Migration: Rename Return Columns to Avoid Conflicts
-- Date: 2025-08-02
-- Description: Rename function return columns to avoid conflicts with table columns

-- Drop all existing group functions
DROP FUNCTION IF EXISTS create_group_and_join(TEXT, TEXT);
DROP FUNCTION IF EXISTS auto_join_group(TEXT, TEXT);

-- <PERSON><PERSON> working create_group_and_join function with renamed return columns
CREATE OR REPLACE FUNCTION create_group_and_join(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  result_group_id UUID,
  result_group_name TEXT,
  result_creator_id UUID,
  result_created_at TIMESTAMP WITH TIME ZONE,
  result_is_member BOOLEAN,
  result_member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_result RECORD;
BEGIN
  -- Get user ID
  SELECT u.id INTO v_user_id FROM users u WHERE u.username = p_username;
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;
  
  -- Create new group and get result
  INSERT INTO groups (name, creator_id)
  VALUES (p_group_name, v_user_id)
  RETURNING groups.id, groups.name, groups.creator_id, groups.created_at 
  INTO v_result;
  
  -- Add creator to group
  INSERT INTO group_members (group_id, user_id)
  VALUES (v_result.id, v_user_id);
  
  -- Return result
  RETURN QUERY
  SELECT 
    v_result.id,
    v_result.name,
    v_result.creator_id,
    v_result.created_at,
    TRUE,
    1::BIGINT;
END;
$$;

-- Create working auto_join_group function with renamed return columns
CREATE OR REPLACE FUNCTION auto_join_group(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  result_group_id UUID,
  result_group_name TEXT,
  result_creator_id UUID,
  result_created_at TIMESTAMP WITH TIME ZONE,
  result_is_member BOOLEAN,
  result_member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_group RECORD;
  v_member_count BIGINT;
BEGIN
  -- Get user ID
  SELECT u.id INTO v_user_id FROM users u WHERE u.username = p_username;
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;
  
  -- Find group by name (case-insensitive)
  SELECT g.id, g.name, g.creator_id, g.created_at
  INTO v_group
  FROM groups g
  WHERE LOWER(g.name) = LOWER(p_group_name);
  
  IF v_group.id IS NULL THEN
    -- Group doesn't exist
    RETURN;
  END IF;
  
  -- Add user to group if not already a member
  INSERT INTO group_members (group_id, user_id)
  VALUES (v_group.id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  -- Get member count
  SELECT COUNT(*) INTO v_member_count FROM group_members gm WHERE gm.group_id = v_group.id;
  
  -- Return result
  RETURN QUERY
  SELECT 
    v_group.id,
    v_group.name,
    v_group.creator_id,
    v_group.created_at,
    TRUE,
    v_member_count;
END;
$$;
