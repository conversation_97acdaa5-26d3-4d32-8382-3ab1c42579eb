-- Migration: Update Group Schema for Better UX Flow
-- Date: 2025-08-02
-- Description: Updates group schema to support search-first flow and auto-joining

-- Drop the existing group_members table to recreate with better structure
DROP TABLE IF EXISTS public.group_members CASCADE;

-- Recreate group_members table with auto-join support
CREATE TABLE public.group_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure a user can only be in a group once
  CONSTRAINT unique_group_member UNIQUE (group_id, user_id)
);

-- Add a function to automatically add user to group when they search and find it
CREATE OR REPLACE FUNCTION auto_join_group(group_name_param TEXT, username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member <PERSON><PERSON><PERSON><PERSON><PERSON>,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_group_id UUID;
  found_user_id UUID;
  group_record RECORD;
BEGIN
  -- Get user ID
  SELECT id INTO found_user_id 
  FROM public.users 
  WHERE username = username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', username_param;
  END IF;
  
  -- Find group by name (case-insensitive)
  SELECT g.id, g.name, g.creator_id, g.created_at
  INTO group_record
  FROM public.groups g
  WHERE LOWER(g.name) = LOWER(group_name_param);
  
  IF group_record.id IS NULL THEN
    -- Group doesn't exist, return empty result
    RETURN;
  END IF;
  
  found_group_id := group_record.id;
  
  -- Auto-join user to group if not already a member
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (found_group_id, found_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  -- Return group info with membership status
  RETURN QUERY
  SELECT 
    group_record.id,
    group_record.name,
    group_record.creator_id,
    group_record.created_at,
    TRUE as is_member,
    (SELECT COUNT(*) FROM public.group_members WHERE group_members.group_id = found_group_id) as member_count;
END;
$$;

-- Add a function to create a new group and auto-add creator
CREATE OR REPLACE FUNCTION create_group_and_join(group_name_param TEXT, creator_username_param TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member BOOLEAN,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  found_user_id UUID;
  new_group_id UUID;
  new_group_record RECORD;
BEGIN
  -- Get user ID
  SELECT id INTO found_user_id 
  FROM public.users 
  WHERE username = creator_username_param;
  
  IF found_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', creator_username_param;
  END IF;
  
  -- Create new group
  INSERT INTO public.groups (name, creator_id)
  VALUES (group_name_param, found_user_id)
  RETURNING id, name, creator_id, created_at INTO new_group_record;
  
  new_group_id := new_group_record.id;
  
  -- Auto-add creator to group
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (new_group_id, found_user_id);
  
  -- Return group info
  RETURN QUERY
  SELECT 
    new_group_record.id,
    new_group_record.name,
    new_group_record.creator_id,
    new_group_record.created_at,
    TRUE as is_member,
    1::BIGINT as member_count;
END;
$$;

-- Add a function to add user to group by username
CREATE OR REPLACE FUNCTION add_user_to_group(group_id_param UUID, username_param TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  found_user_id UUID;
BEGIN
  -- Get user ID
  SELECT id INTO found_user_id 
  FROM public.users 
  WHERE username = username_param;
  
  IF found_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Add user to group
  INSERT INTO public.group_members (group_id, user_id)
  VALUES (group_id_param, found_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  RETURN TRUE;
END;
$$;

-- Update indexes for better performance
CREATE INDEX idx_group_members_group_id ON public.group_members(group_id);
CREATE INDEX idx_group_members_user_id ON public.group_members(user_id);
CREATE INDEX idx_group_members_joined_at ON public.group_members(joined_at DESC);

-- Disable RLS for development (consistent with existing tables)
ALTER TABLE public.group_members DISABLE ROW LEVEL SECURITY;

-- Add helpful comments
COMMENT ON FUNCTION auto_join_group IS 'Searches for a group and auto-joins user if found';
COMMENT ON FUNCTION create_group_and_join IS 'Creates a new group and adds creator as first member';
COMMENT ON FUNCTION add_user_to_group IS 'Adds a user to an existing group by username';
COMMENT ON TABLE public.group_members IS 'Simplified group membership with auto-join support';
