-- Migration: Simple Group Functions
-- Date: 2025-08-02
-- Description: Create simple, working group functions without parameter conflicts

-- Drop all existing group functions
DROP FUNCTION IF EXISTS create_group_and_join(TEXT, TEXT);
DROP FUNCTION IF EXISTS auto_join_group(TEXT, TEXT);
DROP FUNCTION IF EXISTS add_user_to_group(UUID, TEXT);

-- Create simple create_group_and_join function
CREATE OR REPLACE FUNCTION create_group_and_join(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member B<PERSON><PERSON><PERSON><PERSON>,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_group_id UUID;
  v_group_name TEXT;
  v_creator_id UUID;
  v_created_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get user ID
  SELECT id INTO v_user_id FROM users WHERE username = p_username;
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;
  
  -- Create new group
  INSERT INTO groups (name, creator_id)
  VALUES (p_group_name, v_user_id)
  RETURNING id, name, creator_id, created_at 
  INTO v_group_id, v_group_name, v_creator_id, v_created_at;
  
  -- Add creator to group
  INSERT INTO group_members (group_id, user_id)
  VALUES (v_group_id, v_user_id);
  
  -- Return result
  RETURN QUERY
  SELECT 
    v_group_id,
    v_group_name,
    v_creator_id,
    v_created_at,
    TRUE,
    1::BIGINT;
END;
$$;

-- Create simple auto_join_group function
CREATE OR REPLACE FUNCTION auto_join_group(p_group_name TEXT, p_username TEXT)
RETURNS TABLE(
  group_id UUID,
  group_name TEXT,
  creator_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  is_member BOOLEAN,
  member_count BIGINT
) 
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_group_id UUID;
  v_group_name TEXT;
  v_creator_id UUID;
  v_created_at TIMESTAMP WITH TIME ZONE;
  v_member_count BIGINT;
BEGIN
  -- Get user ID
  SELECT id INTO v_user_id FROM users WHERE username = p_username;
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found: %', p_username;
  END IF;
  
  -- Find group by name (case-insensitive)
  SELECT id, name, creator_id, created_at
  INTO v_group_id, v_group_name, v_creator_id, v_created_at
  FROM groups
  WHERE LOWER(name) = LOWER(p_group_name);
  
  IF v_group_id IS NULL THEN
    -- Group doesn't exist
    RETURN;
  END IF;
  
  -- Add user to group if not already a member
  INSERT INTO group_members (group_id, user_id)
  VALUES (v_group_id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  -- Get member count
  SELECT COUNT(*) INTO v_member_count FROM group_members WHERE group_id = v_group_id;
  
  -- Return result
  RETURN QUERY
  SELECT 
    v_group_id,
    v_group_name,
    v_creator_id,
    v_created_at,
    TRUE,
    v_member_count;
END;
$$;

-- Create simple add_user_to_group function
CREATE OR REPLACE FUNCTION add_user_to_group(p_group_id UUID, p_username TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get user ID
  SELECT id INTO v_user_id FROM users WHERE username = p_username;
  
  IF v_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Add user to group
  INSERT INTO group_members (group_id, user_id)
  VALUES (p_group_id, v_user_id)
  ON CONFLICT (group_id, user_id) DO NOTHING;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;
